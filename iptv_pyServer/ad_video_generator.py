#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import shutil
from pathlib import Path
import time
import glob
from flask import Flask, send_from_directory, Response, Blueprint
from waitress import serve
from config.config import cfg
from logger import logger
from utils import get_request_host_url

# 全局路径和文件名配置
TS_PATTERN = 'segment_%03d.ts'  # TS片段文件名模板
TEMP_VIDEO = 'temp_video.mp4'  # 临时视频文件名

# 构建完整路径
IPTV_DIR = str(cfg.path_config.iptv_dir)
M3U8_PATH = os.path.join(IPTV_DIR, cfg.path_config.m3u8_filename)
WEB_ROOT = str(cfg.path_config.iptv_dir.parent)
IPTV_DIR_NAME = os.path.basename(IPTV_DIR)

# 创建广告视频蓝图
ad_video_bp = Blueprint('ad_video', __name__)


def create_ad_video(resolution='1920x1080', fps=5, segment_time=2, duration=10, bitrate='500k'):
    """从广告图片生成视频TS片段并创建M3U8播放列表"""
    # 使用配置类中的图片路径
    image_path = str(cfg.path_config.ad_image_file)

    # 确保图片存在
    if not os.path.exists(image_path):
        logger.error(f"未找到图片文件: {image_path}")
        raise FileNotFoundError(f"未找到图片文件: {image_path}")

    # 如果输出目录不存在则创建
    os.makedirs(IPTV_DIR, exist_ok=True)

    logger.info(f"处理图片: {image_path}, 输出至: {IPTV_DIR}")
    logger.info(
        f"视频参数: {resolution}, {fps}fps, 片段{segment_time}秒, 总长{duration}秒, 比特率{bitrate}")

    # 从图片创建临时视频文件
    temp_video_path = os.path.join(IPTV_DIR, TEMP_VIDEO)
    logger.info("步骤1: 图片转MP4...")
    subprocess.run([
        'ffmpeg', '-y', '-loop', '1', '-i', image_path,
        '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'stillimage',
        '-t', str(duration), '-pix_fmt', 'yuv420p',
        '-r', str(fps), '-s', resolution, '-b:v', bitrate,
        temp_video_path
    ], check=True)

    # 生成TS片段和M3U8文件
    logger.info("步骤2: MP4转HLS...")
    ts_pattern_path = os.path.join(IPTV_DIR, TS_PATTERN)

    # 使用ffmpeg命令创建HLS流
    subprocess.run([
        'ffmpeg', '-y', '-i', temp_video_path,
        '-c:v', 'libx264', '-preset', 'ultrafast',
        '-f', 'hls',
        '-hls_time', str(segment_time),
        '-hls_list_size', '0',
        '-hls_segment_filename', ts_pattern_path,
        M3U8_PATH
    ], check=True)

    # 检查是否成功生成TS文件
    ts_files = glob.glob(os.path.join(IPTV_DIR, "segment_*.ts"))
    if not ts_files:
        logger.error("ffmpeg未能生成TS片段文件，尝试直接自行生成...")
        # 如果ffmpeg未能生成TS片段，我们可以尝试将MP4文件复制为单个TS文件
        single_ts_file = os.path.join(IPTV_DIR, "segment_000.ts")
        shutil.copy(temp_video_path, single_ts_file)
        logger.info(f"已创建单个TS文件: {single_ts_file}")
        # 手动创建最简单的M3U8文件
        manual_create_m3u8(single_ts_file, segment_time, duration)
    else:
        logger.info(f"成功生成{len(ts_files)}个TS片段文件")

    # 移除临时视频文件
    if os.path.exists(temp_video_path):
        os.remove(temp_video_path)

    logger.info(f"M3U8播放列表已创建完成: {M3U8_PATH}")
    return M3U8_PATH


def manual_create_m3u8(ts_file, segment_time, duration):
    """手动创建最简单的M3U8文件，确保播放正常"""
    ts_filename = os.path.basename(ts_file)

    with open(M3U8_PATH, 'w') as f:
        f.write("#EXTM3U\n")
        f.write("#EXT-X-VERSION:3\n")
        f.write(f"#EXT-X-TARGETDURATION:{duration}\n")
        f.write("#EXT-X-MEDIA-SEQUENCE:0\n")
        f.write("#EXT-X-ALLOW-CACHE:YES\n")

        # 将整个视频作为单个片段
        f.write(f"#EXTINF:{duration}.0,\n")
        f.write(f"{ts_filename}\n")

    logger.info(f"已手动创建M3U8播放列表，使用单个TS文件")
    return M3U8_PATH


# Flask蓝图静态文件服务
@ad_video_bp.route('/<path:filename>')
def serve_file(filename):
    """处理所有静态文件请求，包括M3U8和TS文件"""
    # 获取文件扩展名
    _, ext = os.path.splitext(filename)

    # 设置正确的MIME类型
    mimetype = None
    if ext == '.m3u8':
        mimetype = 'application/vnd.apple.mpegurl'
        # 为M3U8文件做特殊处理，生成动态更新的播放列表
        if filename == cfg.path_config.m3u8_filename:
            return serve_dynamic_m3u8()
    elif ext == '.ts':
        mimetype = 'video/mp2t'

    # 直接从IPTV_DIR提供文件，这是实际存放所有文件的地方
    try:
        return send_from_directory(IPTV_DIR, filename, mimetype=mimetype)
    except Exception as e:
        logger.error(f"提供文件{filename}失败: {str(e)}")
        return Response("File not found", status=404)


def serve_dynamic_m3u8():
    """提供动态生成的M3U8内容，实现真正的无限循环播放"""
    # 获取所有TS文件
    ts_files = glob.glob(os.path.join(IPTV_DIR, "segment_*.ts"))

    if not ts_files:
        logger.error("没有找到TS文件，无法生成M3U8")
        return Response("No TS files found", status=500)

    # 使用相对路径，这是关键点
    ts_filenames = [os.path.basename(f) for f in ts_files]
    ts_filenames.sort()  # 确保顺序正确

    # 创建M3U8内容
    m3u8_content = "#EXTM3U\n"
    m3u8_content += "#EXT-X-VERSION:3\n"
    m3u8_content += "#EXT-X-TARGETDURATION:3\n"
    # 使用时间戳作为序列号，确保播放器认为这是新内容
    m3u8_content += f"#EXT-X-MEDIA-SEQUENCE:{int(time.time() % 1000000)}\n"
    m3u8_content += "#EXT-X-ALLOW-CACHE:NO\n"

    # 添加所有片段
    for filename in ts_filenames:
        m3u8_content += "#EXTINF:2.0,\n"
        m3u8_content += f"{filename}\n"

    response = Response(m3u8_content, mimetype='application/vnd.apple.mpegurl')
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response


def init_ad_video(app, port=None):
    """初始化广告视频功能并注册到Flask应用"""
    # 生成视频和m3u8播放列表
    try:
        m3u8_path = create_ad_video(segment_time=2, bitrate='500k')

        # 获取当前使用的端口
        actual_port = port or cfg.server_config.port

        # 构建流地址
        server_url = get_request_host_url(actual_port)
        stream_url = f"{server_url}/{IPTV_DIR_NAME}/{cfg.path_config.m3u8_filename}"

        logger.info(f"广告视频流已初始化，流地址: {stream_url}")

        # 注册蓝图到Flask应用
        app.register_blueprint(ad_video_bp, url_prefix='/'+IPTV_DIR_NAME)

        return True
    except Exception as e:
        logger.error(f"初始化广告视频流失败: {str(e)}")
        return False


# 当直接运行此脚本时，创建独立的Flask应用进行测试
if __name__ == "__main__":
    # 创建一个测试用的Flask应用
    app = Flask(__name__)

    # 初始化广告视频功能
    init_ad_video(app, port=8081)

    # 启动测试服务器
    logger.info("启动测试服务器...")
    serve(app, host='0.0.0.0', port=8081, threads=4)
