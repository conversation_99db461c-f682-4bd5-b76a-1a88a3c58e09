#!/bin/bash
set -e

echo "Starting container setup..."

# 显示系统信息
echo "System information:"
uname -a
python --version
pip --version
echo "Timezone: $(date)"

# 检查核心Python库
echo "Checking required Python modules..."
python -c "import waitress; print('Waitress module found')" || echo "ERROR: Waitress import failed"
python -c "import flask; print('Flask module found')" || echo "ERROR: Flask import failed"

# 如果config目录为空，则复制默认配置
if [ -z "$(ls -A /app/config)" ]; then
  echo "Config directory is empty, copying default configs..."
  cp -r /app/default_config/* /app/config/
fi

# 确保config.py文件存在于config目录中
if [ ! -f "/app/config/config.py" ]; then
  echo "config.py not found in config directory, copying from default_config..."
  cp -f /app/default_config/config.py /app/config/ 2>/dev/null || echo "Warning: Could not copy config.py from default_config"
fi

# 确保ad.jpeg文件存在于config目录中
if [ ! -f "/app/config/ad.jpeg" ]; then
  echo "ad.jpeg not found in config directory, copying from default_config..."
  cp -f /app/default_config/ad.jpeg /app/config/ 2>/dev/null || echo "Warning: Could not copy ad.jpeg from default_config"
fi

# 确保目录存在（不修改权限，避免挂载目录权限冲突）
echo "Setting up directories..."
mkdir -p /app/iptv /app/logs

# 确保subscriptions.json存在（不修改权限）
touch /app/config/subscriptions.json 2>/dev/null || echo "Warning: Could not create subscriptions.json"

# 检查web目录
if [ -d "/app/web" ]; then
  echo "Web directory found"
else
  echo "Warning: Web directory not found"
fi

echo "Container setup complete. Starting application..."

# 配置日志
echo "Configuring logging..."
mkdir -p /app/logs
touch /app/logs/app.log 2>/dev/null || echo "Warning: Could not create log file"

# 启动服务
echo "Starting Waitress server with app.py..."
exec python app.py 2>&1 | tee -a /app/logs/app.log