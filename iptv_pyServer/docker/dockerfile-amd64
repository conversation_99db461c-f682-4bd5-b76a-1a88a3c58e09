# 使用多阶段构建以减小镜像大小
FROM python:3.10-slim-bullseye AS builder

# 设置工作目录
WORKDIR /app

# 配置apt源和pip源，安装构建依赖
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    cmake \
    git \
    wget \
    libopencv-dev \
    python3-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/* \
    && pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 复制依赖文件
COPY requirements.txt .

# 安装依赖，确保使用特定版本以避免兼容性问题
RUN pip install --no-cache-dir wheel setuptools \
    && pip install --no-cache-dir numpy==1.24.3 \
    && pip install --no-cache-dir pandas==2.0.3 \
    && pip install --no-cache-dir opencv-python-headless==******** \
    # 先安装requirements中除了opencc外的所有依赖
    && grep -v "opencc" requirements.txt > requirements_no_opencc.txt \
    && pip install --no-cache-dir -r requirements_no_opencc.txt \
    # 最后安装指定版本的opencc，确保不会被覆盖
    && pip install --no-cache-dir opencc==1.1.4

# 第二阶段：最终镜像
FROM python:3.10-slim-bullseye

# 设置工作目录
WORKDIR /app

# 设置时区为Asia/Shanghai
ENV TZ=Asia/Shanghai

# 安装运行时依赖
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
    ffmpeg \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender1 \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p /app/iptv /app/config

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser -d /app -s /bin/bash appuser

# 从builder阶段复制Python环境
# 第一行复制Python库文件和模块
COPY --from=builder /usr/local/lib/python3.10/site-packages/ /usr/local/lib/python3.10/site-packages/
# 第二行复制可执行文件
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# 复制应用代码（移除wsgi.py，添加ad_video_generator.py）
COPY app.py iptv_checker.py check_streams.py get_iptv_to_df.py speed_test.py logger.py subscription.py utils.py ad_video_generator.py ./

# 复制网页模块
COPY web/ /app/web/

# 复制默认配置文件到特定的默认配置目录（确保包含ad.jpeg）
COPY config/ /app/default_config/

# 创建config目录并确保config.py和ad.jpeg存在
RUN mkdir -p /app/config /app/logs
COPY config/config.py /app/config/
COPY config/ad.jpeg /app/config/

# 复制启动脚本并设置权限
COPY docker/entrypoint.sh /
RUN chmod +x /entrypoint.sh

# 设置目录权限
RUN chown -R appuser:appuser /app

# 设置挂载点
VOLUME ["/app/config", "/app/iptv", "/app/logs"]

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 切换到非root用户
USER appuser

# 使用自定义入口点
ENTRYPOINT ["/entrypoint.sh"]