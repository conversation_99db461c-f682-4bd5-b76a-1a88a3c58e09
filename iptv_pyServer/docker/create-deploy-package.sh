#!/bin/bash

echo "=========================================="
echo "  在macOS上构建Ubuntu部署包"
echo "=========================================="

# 检查当前架构
CURRENT_ARCH=$(uname -m)
if [ "$CURRENT_ARCH" != "arm64" ]; then
    echo "警告: 此脚本设计在macOS ARM64上运行"
    echo "当前架构: $CURRENT_ARCH"
fi

# 确保buildx可用
if ! docker buildx version &>/dev/null; then
    echo "❌ Docker Buildx 不可用，请更新Docker Desktop"
    exit 1
fi

# 设置部署包目录
DEPLOY_DIR="iptv_server_docker_compose"

# 清理旧的部署包
rm -rf $DEPLOY_DIR

echo "步骤1: 构建Ubuntu AMD64镜像..."
docker buildx build --platform linux/amd64 -t iptv-server:ubuntu-amd64 -f dockerfile-amd64 .. --load

if [ $? -ne 0 ]; then
    echo "❌ 镜像构建失败"
    exit 1
fi

echo "✅ 镜像构建成功"

# 创建部署包目录
echo "步骤2: 创建部署包目录..."
mkdir -p $DEPLOY_DIR

echo "步骤3: 导出Docker镜像..."
docker save iptv-server:ubuntu-amd64 | gzip > $DEPLOY_DIR/iptv-server-ubuntu-amd64.tar.gz

echo "步骤4: 复制部署配置文件..."

# 创建docker-compose.yml文件
cat > $DEPLOY_DIR/docker-compose.yml << 'EOF'
services:
  iptv-server:
    image: iptv-server:ubuntu-amd64
    container_name: iptv-server
    restart: unless-stopped
    user: "0:0"
    ports:
      - "8000:8000"
    volumes:
      - ./config:/app/config
      - ./iptv:/app/iptv
      - ./logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    pull_policy: never
EOF

echo "步骤5: 创建导入镜像脚本..."
cat > $DEPLOY_DIR/load-image.sh << 'EOF'
#!/bin/bash

echo "=========================================="
echo "  导入IPTV服务器镜像"
echo "=========================================="

# 检查镜像是否已存在
if docker images | grep -q "iptv-server.*ubuntu-amd64"; then
    echo "检测到已存在的镜像，正在删除..."
    docker rmi iptv-server:ubuntu-amd64 2>/dev/null || true
    echo "✅ 旧镜像已删除"
fi

echo "导入新镜像..."
gunzip -c iptv-server-ubuntu-amd64.tar.gz | docker load

if [ $? -eq 0 ]; then
    echo "✅ 镜像导入成功"
    echo ""
    echo "镜像信息:"
    docker images | grep "iptv-server.*ubuntu-amd64"
    echo ""
    echo "下一步:"
    echo "  docker compose up -d"
    echo ""
    echo "管理命令:"
    echo "  docker compose ps       # 查看状态"
    echo "  docker compose logs -f  # 查看日志"
    echo "  docker compose down     # 停止服务"
else
    echo "❌ 镜像导入失败"
    exit 1
fi
EOF

chmod +x $DEPLOY_DIR/load-image.sh

echo "步骤6: 创建说明文档..."
cat > $DEPLOY_DIR/README.md << 'EOF'
# IPTV服务器 Ubuntu部署包

## 系统要求
- Ubuntu 18.04+ (AMD64架构)
- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB内存
- 至少5GB磁盘空间

## 部署步骤

### 1. 导入镜像
```bash
./load-image.sh
```

### 2. 启动服务
```bash
docker compose up -d
```

### 3. 查看状态
```bash
docker compose ps
```

## 服务管理

```bash
# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 查看服务状态
docker compose ps
```

## 访问服务

- Web界面: http://localhost:8000
- 健康检查: http://localhost:8000/health

## 数据目录

- `config/` - 配置文件
- `iptv/` - IPTV文件输出
- `logs/` - 日志文件

## 故障排除

1. **端口冲突**: 修改 docker-compose.yml 中的端口映射
2. **权限问题**: 确保用户在docker组中 `sudo usermod -aG docker $USER`
3. **内存不足**: 检查系统内存使用情况
4. **磁盘空间**: 清理Docker资源 `docker system prune -a`
EOF

# 创建目录占位文件
mkdir -p $DEPLOY_DIR/config $DEPLOY_DIR/iptv $DEPLOY_DIR/logs
touch $DEPLOY_DIR/iptv/.gitkeep
touch $DEPLOY_DIR/logs/.gitkeep



echo ""
echo "✅ Ubuntu部署包创建完成!"
echo ""
echo "部署包信息:"
echo "  - 目录: $DEPLOY_DIR/"
echo "  - 镜像大小: $(du -h $DEPLOY_DIR/iptv-server-ubuntu-amd64.tar.gz | cut -f1)"
echo ""
echo "部署包内容:"
ls -la $DEPLOY_DIR/
echo ""
echo "使用方法:"
echo "1. 将 $DEPLOY_DIR/ 目录拷贝到Ubuntu服务器"
echo "2. 在Ubuntu服务器上执行:"
echo "   cd $DEPLOY_DIR"
echo "   ./load-image.sh"
echo "   docker compose up -d"
echo ""
echo "注意: 此部署包包含预构建的Docker镜像，无需在Ubuntu服务器上重新构建"
