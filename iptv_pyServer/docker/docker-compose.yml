services:
  iptv-server:
    image: iptv-server:ubuntu-amd64
    container_name: iptv-server
    restart: unless-stopped
    user: "0:0"
    ports:
      - "8000:8000"
    volumes:
      - ./config:/app/config
      - ./iptv:/app/iptv
      - ./logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    pull_policy: never
