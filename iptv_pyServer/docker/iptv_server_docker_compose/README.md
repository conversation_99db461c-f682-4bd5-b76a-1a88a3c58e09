# IPTV服务器 Ubuntu部署包

## 系统要求
- Ubuntu 18.04+ (AMD64架构)
- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB内存
- 至少5GB磁盘空间

## 部署步骤

### 1. 导入镜像
```bash
./load-image.sh
```

### 2. 启动服务
```bash
docker compose up -d
```

### 3. 查看状态
```bash
docker compose ps
```

## 服务管理

```bash
# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 查看服务状态
docker compose ps
```

## 访问服务

- Web界面: http://localhost:8000
- 健康检查: http://localhost:8000/health

## 数据目录

- `config/` - 配置文件
- `iptv/` - IPTV文件输出
- `logs/` - 日志文件

## 故障排除

1. **端口冲突**: 修改 docker-compose.yml 中的端口映射
2. **权限问题**: 确保用户在docker组中 `sudo usermod -aG docker $USER`
3. **内存不足**: 检查系统内存使用情况
4. **磁盘空间**: 清理Docker资源 `docker system prune -a`
