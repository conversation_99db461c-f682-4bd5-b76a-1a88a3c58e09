#!/bin/bash

echo "=========================================="
echo "  导入IPTV服务器镜像"
echo "=========================================="

# 检查镜像是否已存在
if docker images | grep -q "iptv-server.*ubuntu-amd64"; then
    echo "检测到已存在的镜像，正在删除..."
    docker rmi iptv-server:ubuntu-amd64 2>/dev/null || true
    echo "✅ 旧镜像已删除"
fi

echo "导入新镜像..."
gunzip -c iptv-server-ubuntu-amd64.tar.gz | docker load

if [ $? -eq 0 ]; then
    echo "✅ 镜像导入成功"
    echo ""
    echo "镜像信息:"
    docker images | grep "iptv-server.*ubuntu-amd64"
    echo ""
    echo "下一步:"
    echo "  docker compose up -d"
    echo ""
    echo "管理命令:"
    echo "  docker compose ps       # 查看状态"
    echo "  docker compose logs -f  # 查看日志"
    echo "  docker compose down     # 停止服务"
else
    echo "❌ 镜像导入失败"
    exit 1
fi
