# Docker 部署目录

这个目录包含了IPTV服务器的Docker部署相关文件。

## 目录结构

```
docker/
├── docker-compose.yml        # Docker Compose配置文件
├── dockerfile-amd64          # Linux AMD64 Dockerfile
├── create-deploy-package.sh  # Ubuntu部署包生成脚本
├── entrypoint.sh             # 容器启动脚本
└── README.md                 # 本文件
```

## 快速开始

### 推荐方式：使用Ubuntu部署包
```bash
# 创建Ubuntu部署包
./create-deploy-package.sh

# 上传部署包到Ubuntu服务器
scp -r iptv_server_docker_compose/ user@ubuntu-server:/opt/

# 在Ubuntu服务器上部署
ssh user@ubuntu-server
cd /opt/iptv_server_docker_compose/
./load-image.sh          # 自动删除旧镜像并导入新镜像
docker compose up -d
```

**load-image.sh特点：**
- 自动检测并删除已存在的镜像
- 支持镜像更新和覆盖部署
- 显示详细的导入信息和管理命令

### 直接使用docker目录（开发环境）
```bash
# 构建并启动服务
docker compose up -d

# 查看服务状态
docker compose ps

# 停止服务
docker compose down
```

## 常用命令

### 服务管理
```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 重新构建并启动
docker compose up -d --build
```

### 构建镜像

#### 本地构建
```bash
# 手动构建镜像
docker build -f dockerfile-amd64 -t iptv-server:latest ..
```

## 数据持久化

以下目录会被挂载到宿主机：

- `./config` -> `/app/config` (配置文件)
- `./iptv` -> `/app/iptv` (IPTV文件)
- `./logs` -> `/app/logs` (日志文件)

## 网络访问

- Web界面: http://localhost:8000
- 健康检查: http://localhost:8000/health

## 故障排除

如果遇到问题，请查看：

1. 服务日志: `docker compose logs -f`
2. 容器状态: `docker compose ps`
3. 健康检查: `curl http://localhost:8000/health`
