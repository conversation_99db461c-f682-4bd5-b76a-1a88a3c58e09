# IPTV 服务器

## 开发环境

使用 Flask 内置服务器运行（仅用于开发）：

```bash
python app.py
```

## 生产环境部署

### 使用 Waitress 服务器

生产环境中使用 Waitress 作为 WSGI 服务器，应用会自动启动 Waitress 服务器：

```bash
python app.py
```

Waitress 是一个纯 Python 实现的生产级服务器，不需要额外的编译依赖。

### 配置服务器

服务器参数在 `config/config.py` 文件的 `ServerConfig` 类中配置：

```python
class ServerConfig(BaseModel):
    """服务器配置类"""
    host: str = "0.0.0.0"  # 监听地址
    port: int = 8000       # 监听端口
    threads: int = 4       # 工作线程数
    connection_limit: int = 1000  # 最大并发连接数
    cleanup_interval: int = 30    # 清理间隔（秒）
    channel_timeout: int = 120    # 通道超时（秒）
```

### 使用 Docker 部署（推荐）

#### 方法1: 使用Ubuntu部署包（推荐生产环境）

```bash
# 1. 在macOS上构建Ubuntu部署包
cd docker
./create-deploy-package.sh

# 2. 拷贝部署文件夹到Ubuntu服务器
scp -r iptv_server_docker_compose/ user@ubuntu-server:/opt/

# 3. 在Ubuntu服务器上部署
ssh user@ubuntu-server
cd /opt/iptv_server_docker_compose/
./load-image.sh          # 自动删除旧镜像并导入新镜像
docker compose up -d
```

**部署包特点：**
- 包含预构建的Docker镜像（压缩后约384MB）
- 自动覆盖已存在的镜像，支持更新部署
- 无需在Ubuntu服务器上重新构建
- 支持离线部署
- 使用标准的docker compose命令管理

#### 方法2: 直接使用docker目录

```bash
# 进入docker目录
cd docker

# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### Docker 部署特性

- **跨平台构建**: 在macOS上构建Ubuntu AMD64镜像
- **镜像部署**: 预构建镜像，无需在目标服务器重新构建
- **一键部署**: 使用部署包脚本自动完成所有部署步骤
- **健康检查**: 自动监控服务健康状态
- **日志管理**: 自动日志轮转，限制日志文件大小
- **数据持久化**: 配置、IPTV文件和日志的持久化存储
- **重启策略**: 容器异常退出时自动重启

### 使用 Docker 手动部署

如果需要手动构建和运行：

```bash
# 构建镜像
docker build -f docker/dockerfile-amd64 -t iptv-server:latest .

# 运行容器
docker run -d --name iptv-server \
  -p 8000:8000 \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/iptv:/app/iptv \
  -v $(pwd)/logs:/app/logs \
  --restart unless-stopped \
  iptv-server:latest
```

Docker 镜像使用 Waitress 作为生产服务器，自动加载所有必要的组件。

## 目录结构

部署后的目录结构：

```
iptv_pyServer/
├── config/                    # 配置文件目录（持久化）
├── iptv/                     # IPTV文件目录（持久化）
├── logs/                     # 日志文件目录（持久化）
├── web/                      # Web界面文件
├── docker/                   # Docker部署相关文件
│   ├── docker-compose.yml    # Docker Compose配置
│   ├── dockerfile-amd64      # Linux AMD64 Dockerfile
│   └── README.md             # Docker目录说明
├── create-deploy-package.sh  # 创建Linux部署包脚本
└── entrypoint.sh             # 容器启动脚本
```

## 服务管理

### 服务管理

#### 使用部署包
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
./stop.sh
```

#### 使用docker目录
```bash
# 进入docker目录
cd docker

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs iptv-server

   # 检查容器状态
   docker-compose ps
   ```

2. **健康检查失败**
   ```bash
   # 手动测试健康检查端点
   curl http://localhost:8000/health

   # 查看容器内部状态
   docker-compose exec iptv-server /bin/bash
   ```

3. **权限问题**
   ```bash
   # 检查目录权限
   ls -la config/ iptv/ logs/

   # 修复权限（如果需要）
   sudo chown -R $USER:$USER config/ iptv/ logs/
   ```

4. **构建问题**
   ```bash
   # 清理Docker缓存重新构建
   docker system prune -a
   docker-compose build --no-cache
   docker-compose up -d
   ```

### 日志查看

- **应用日志**: `logs/app.log`
- **Docker日志**: `docker-compose logs iptv-server`
- **实时日志**: `docker-compose logs -f iptv-server`

## SSH 反向代理设置

使用以下命令建立 SSH 反向代理（外网访问）：

```bash
ssh -R 39999:127.0.0.1:8000 -p 40002 -f -C -q -N administrator@************** -o ServerAliveInterval=30 -o ServerAliveCountMax=3
```

## 安全说明

- 容器使用非root用户运行，提高安全性
- 健康检查确保服务可用性
- 日志轮转防止磁盘空间耗尽
- 数据目录持久化，避免数据丢失