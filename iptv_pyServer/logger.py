#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
import inspect
from config.config import cfg

# 默认日志级别
DEFAULT_LOG_LEVEL = cfg.param_config.log_level

# 日志格式
LOG_FORMAT = "%(asctime)s [%(levelname)s] %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 确保日志目录存在
os.makedirs(cfg.path_config.log_dir, exist_ok=True)

# 默认配置
DEFAULT_CONFIG = {
    'log_dir': cfg.path_config.log_dir,  # 日志保存路径
    'log_file': 'iptv-server.log',  # 日志文件名
    'max_bytes': 10 * 1024 * 1024,  # 单个日志文件最大大小 (10MB)
    'backup_count': 5,  # 备份文件数量
    'console': True,  # 是否输出到控制台
    'file': True,  # 是否输出到文件
    'level': DEFAULT_LOG_LEVEL  # 日志级别
}

# 颜色代码
COLORS = {
    'DEBUG': '\033[94m',     # 蓝色
    'INFO': '\033[92m',      # 绿色
    'WARNING': '\033[93m',   # 黄色
    'ERROR': '\033[91m',     # 红色
    'ENDC': '\033[0m'        # 结束颜色
}

# 生成带颜色的格式化器


class ColoredFormatter(logging.Formatter):
    def format(self, record):
        # 创建记录的副本，避免修改原始记录
        record_copy = logging.makeLogRecord(record.__dict__)

        levelname = record_copy.levelname
        if levelname in COLORS:
            record_copy.levelname = f"{COLORS[levelname]}{levelname}{COLORS['ENDC']}"

        return super().format(record_copy)


class Logger:
    _instance = None

    @classmethod
    def get_instance(cls, **kwargs):
        """获取单例模式的日志记录器实例"""
        if cls._instance is None:
            cls._instance = cls(**kwargs)
        return cls._instance

    def __init__(self, **kwargs):
        """初始化日志记录器"""
        # 合并配置
        self.config = DEFAULT_CONFIG.copy()
        self.config.update(kwargs)

        # 创建日志记录器
        self.logger = logging.getLogger('iptv-server')
        self.logger.setLevel(self.config['level'])
        self.logger.propagate = False

        # 清除之前的处理器
        if self.logger.handlers:
            self.logger.handlers.clear()

        # 添加控制台处理器 - 使用彩色格式
        if self.config['console']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.config['level'])
            console_formatter = ColoredFormatter(LOG_FORMAT, DATE_FORMAT)
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

        # 添加文件处理器 - 使用普通格式，不带颜色代码
        if self.config['file']:
            # 确保日志目录存在
            log_dir = self.config['log_dir']
            os.makedirs(log_dir, exist_ok=True)

            # 文件路径
            log_path = os.path.join(log_dir, self.config['log_file'])

            # 创建文件处理器
            file_handler = RotatingFileHandler(
                log_path,
                maxBytes=self.config['max_bytes'],
                backupCount=self.config['backup_count'],
                encoding='utf-8'
            )
            file_handler.setLevel(self.config['level'])
            # 使用普通格式，不带颜色代码
            file_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

    def _log(self, level, msg, *args, **kwargs):
        """记录日志，不再添加调用者信息"""
        # 获取调用堆栈但不使用
        frames = inspect.stack()

        # 记录日志
        if args:
            msg = msg % args

        # 直接记录消息，不添加调用者信息
        self.logger.log(level, msg, **kwargs)

    def debug(self, msg, *args, **kwargs):
        """调试日志"""
        self._log(logging.DEBUG, msg, *args, **kwargs)

    def info(self, msg, *args, **kwargs):
        """信息日志"""
        self._log(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        """警告日志"""
        self._log(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        """错误日志"""
        self._log(logging.ERROR, msg, *args, **kwargs)

    def exception(self, msg, *args, **kwargs):
        """异常日志，包含堆栈信息"""
        kwargs['exc_info'] = True
        self._log(logging.ERROR, msg, *args, **kwargs)


# 创建默认日志记录器
logger = Logger.get_instance()

# 简便函数：直接用作 print 的替代


def debug(msg, *args, **kwargs):
    """调试日志，可直接替代 print 使用"""
    logger.debug(msg, *args, **kwargs)


def info(msg, *args, **kwargs):
    """信息日志，可直接替代 print 使用"""
    logger.info(msg, *args, **kwargs)


def warning(msg, *args, **kwargs):
    """警告日志"""
    logger.warning(msg, *args, **kwargs)


def error(msg, *args, **kwargs):
    """错误日志"""
    logger.error(msg, *args, **kwargs)


def exception(msg, *args, **kwargs):
    """异常日志，包含堆栈信息"""
    logger.exception(msg, *args, **kwargs)

# 完全兼容 print 函数的调用方式，方便替换


def print_log(*args, **kwargs):
    """完全兼容 print 函数的接口，用于直接替换 print"""
    # 处理 print 的分隔符
    sep = kwargs.get('sep', ' ')
    end = kwargs.get('end', '\n')

    # 将所有参数转换为字符串并用分隔符连接
    msg = sep.join(str(arg) for arg in args)
    if end and end != '\n':
        msg += end

    # 输出日志
    logger.info(msg)

    # 如果有 file 参数，还需要写入文件
    file = kwargs.get('file')
    if file and file is not sys.stdout and file is not sys.stderr:
        print(*args, **kwargs)

# 替换内置 print 功能，谨慎使用
# import builtins
# builtins.print = print_log
