#!/usr/bin/env python
# -*- coding: utf-8 -*-

from config.config import cfg
from flask import Flask, jsonify, send_file, request, Response
import os
import threading
import time
import schedule
import json
from waitress import serve
from iptv_checker import iptv_checker, retest_timeout_url
from get_iptv_to_df import get_iptv_to_df
from check_streams import check_streams
from logger import logger
from subscription import (
    is_subscription_valid,
    cleanup_expired_subscriptions,
    process_json_file,
    process_m3u_file
)
from web import init_web
from ad_video_generator import init_ad_video

# 创建Flask应用
app = Flask(__name__)


def run_sequential_tasks():
    get_iptv_to_df()
    check_streams()
    iptv_checker()


def run_scheduler():
    retest_timeout_url()
    schedule.every(3).days.at("01:00").do(retest_timeout_url)
    schedule.every().day.at("08:00").do(run_sequential_tasks)
    schedule.every().day.at("18:00").do(run_sequential_tasks)
    schedule.every().day.at("23:59").do(cleanup_expired_subscriptions)
    while True:
        schedule.run_pending()
        time.sleep(60)


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        return jsonify({
            'status': 'healthy',
            'timestamp': time.time(),
            'service': 'iptv-server'
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500


@app.route('/ott/<filename>', methods=['GET'])
def get_ott_file(filename):
    """获取config/ott目录下的m3u文件，通过查询参数token验证"""
    try:
        # 获取token参数,验证订阅是否有效
        token = request.args.get('token')
        token_valid = token and is_subscription_valid(token)
        log_message = "访问授权OTT文件" if token_valid else "访问非授权OTT文件"

        # 构建config/ott目录下的文件路径
        ott_dir = os.path.join(os.path.dirname(__file__), 'config', 'ott')
        full_path = os.path.join(ott_dir, filename)

        logger.info(f"{log_message}: {token} -> {full_path}")

        if not os.path.exists(full_path):
            return jsonify({'error': 'OTT file not found'}), 404

        # OTT文件都是m3u格式
        return process_m3u_file(full_path, token_valid)
    except Exception as e:
        logger.error(f"访问OTT文件{filename}失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/<filename>', methods=['GET'])
def get_m3u_file(filename):
    """获取m3u文件，通过查询参数token验证"""
    try:
        # 获取token参数,验证订阅是否有效
        token = request.args.get('token')
        token_valid = token and is_subscription_valid(token)
        log_message = "访问授权文件" if token_valid else "访问非授权文件"
        full_path = os.path.join(cfg.path_config.iptv_dir, filename)
        logger.info(f"{log_message}: {token} -> {full_path}")
        if not os.path.exists(full_path):
            return jsonify({'error': 'File not found'}), 404

        # 检查是否是 JSON 文件
        if filename.endswith('.json'):
            return process_json_file(full_path, token)
        else:
            return process_m3u_file(full_path, token_valid)
    except Exception as e:
        logger.error(f"访问文件{filename}失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


def init_app():
    logger.info("启动 IPTV 服务器...")

    # 初始化网页功能 (可选)
    if cfg.server_config.enable_web:
        try:
            init_web(app)
            logger.info("网页功能已启用")
        except ImportError as e:
            logger.warning(f"无法启用网页功能: {str(e)}")
            cfg.server_config.enable_web = False

    # 初始化广告视频功能 (可选)
    if cfg.server_config.enable_ad_video:
        try:
            if init_ad_video(app):
                logger.info("广告视频功能已启用")
            else:
                logger.warning("广告视频功能启用失败")
                cfg.server_config.enable_ad_video = False
        except Exception as e:
            logger.warning(f"无法启用广告视频功能: {str(e)}")
            cfg.server_config.enable_ad_video = False

    # 启动后台任务线程
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    logger.info("后台调度线程已启动")


def get_local_ip():
    """获取本机IP地址"""
    import socket
    try:
        # 创建一个UDP socket连接到外部地址来获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"


def start_server():
    """使用Waitress启动服务器"""
    logger.info(
        f"启动Waitress服务器于 {cfg.server_config.host}:{cfg.server_config.port}")
    logger.info(f"工作线程: {cfg.server_config.threads}")
    logger.info(f"连接限制: {cfg.server_config.connection_limit}")

    # 显示访问地址
    if cfg.server_config.enable_web:
        if cfg.server_config.host == "0.0.0.0":
            local_ip = get_local_ip()
            logger.info(f"网页访问地址: http://localhost:{cfg.server_config.port}/web/")
            logger.info(f"或者使用本机IP: http://{local_ip}:{cfg.server_config.port}/web/")
        else:
            logger.info(f"网页访问地址: http://{cfg.server_config.host}:{cfg.server_config.port}/web/")

    # 使用config.py中的配置启动Waitress
    serve(
        app,
        host=cfg.server_config.host,
        port=cfg.server_config.port,
        threads=cfg.server_config.threads,
        connection_limit=cfg.server_config.connection_limit,
        cleanup_interval=cfg.server_config.cleanup_interval,
        channel_timeout=cfg.server_config.channel_timeout
    )


if __name__ == "__main__":
    init_app()
    # 替换Flask内置服务器为Waitress
    start_server()
