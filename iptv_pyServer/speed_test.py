import time
import av
import subprocess
import json
from statistics import mean
import matplotlib.pyplot as plt
import numpy as np
import cv2
import urllib.request
import ssl
from http.client import HTTPConnection
from email.utils import parsedate_to_datetime
from logger import logger
from urllib.parse import urlparse, quote
import threading

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS',
                                   'SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

TEST_ROUNDS = 20                                  # 每种方法测试轮次
HTTPConnection._MAXHEADERS = 1000  # 增加头部限制

ContentType = ['vnd.apple.mpegurl', 'x-mpegurl',
               'vnd.apple.mpegusr', 'video/mpeg', 'octet-stream']


def _prepare_url_and_context(url):
    """准备URL编码和SSL上下文"""
    parsed_url = urlparse(url)
    # 解决中文的编码问题
    encoded_path = quote(parsed_url.path, safe='/:@&=+$?,')
    encoded_url = parsed_url._replace(path=encoded_path).geturl()
    # 创建一个不验证SSL证书的上下文
    context = ssl._create_unverified_context()
    return encoded_url, context


def check_url_connectivity(url, timeout=3, redirect_count=0, max_redirects=3):
    try:
        # 检查URL协议，如果不是http或https，直接返回True
        if not url.startswith(('http://', 'https://')):
            return True, url

        # 防止无限重定向
        if redirect_count >= max_redirects:
            return False, url

        encoded_url, context = _prepare_url_and_context(url)

        request = urllib.request.Request(encoded_url)  # 使用编码后的URL
        response = urllib.request.urlopen(
            request, timeout=timeout, context=context)
        if response.status != 200:
            return False, url

        # 检查是否有重定向
        if response.url != encoded_url:  # 比较编码后的URL
            # 注意：response.url 可能是编码后的，也可能是服务器解码后的，这里递归调用时传递原始未编码的 response.url
            # 因为 check_url_connectivity 函数内部会再次进行编码
            return check_url_connectivity(response.url, timeout, redirect_count + 1, max_redirects)
        # 获取并解析时间
        date_str = response.headers.get('Date')
        last_modified_str = response.headers.get('Last-Modified')
        if date_str and last_modified_str:
            date_time = parsedate_to_datetime(date_str)
            last_modified_time = parsedate_to_datetime(last_modified_str)
            time_diff = (date_time - last_modified_time).total_seconds()
            if time_diff > 100:
                return False, url
        ct = response.headers['Content-Type'].lower()
        # logger.info(f"Content-Type: {ct}")
        return any(content_type in ct for content_type in ContentType), url
    except Exception as e:
        # logger.error(f"Error checking {url}: {e}")
        return False, url


def speed_av(url, timeout=5):
    try:
        with av.open(
            url,
            # options={  # 加快测试速度,但是对于部分链接可能会导致程序崩溃,并且try except无法捕获
            #     "probesize": "8192"
            # },
            timeout=timeout
        ) as container:
            if container.streams.video:
                stream = container.streams.video[0]
                return stream.width
    except Exception as e:
        # logger.error(f"Error checking {url}: {e}")
        pass
    return 0


def speed_ffprobe(url, timeout=5):
    try:
        cmd = [
            "ffprobe",
            "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height",
            "-of", "json",
            # "-probesize", "8192",
            url
        ]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
        info = json.loads(result.stdout)
        if "streams" in info and info["streams"]:
            return info['streams'][0]['width']
    except:
        pass
    return 0


def speed_opencv(url, timeout=5):
    """使用OpenCV读取流并获取其宽度，确保在timeout秒内返回结果

    通过创建一个单独的线程来实现可靠的超时控制
    """
    result = [0]  # 使用列表存储结果以便在线程中修改
    event = threading.Event()
    cap_ref = [None]  # 使用列表存储cap引用以便在主线程中清理

    def worker():
        cap = None
        try:
            cap = cv2.VideoCapture(url, cv2.CAP_FFMPEG)
            cap_ref[0] = cap  # 保存引用供主线程清理
            if cap.isOpened():
                # 尝试读取一帧以确认流是否可用
                ret, _ = cap.read()
                if ret:
                    result[0] = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        except Exception:
            pass
        finally:
            if cap is not None:
                try:
                    cap.release()
                except Exception:
                    pass
            event.set()  # 通知主线程工作已完成

    # 创建并启动工作线程
    thread = threading.Thread(target=worker)
    thread.daemon = True  # 设置为守护线程，这样如果主线程退出，它也会退出
    thread.start()

    # 等待线程完成或超时
    thread.join(timeout)

    # 如果工作没有完成（超时），强制清理资源
    if not event.is_set():
        # 尝试清理可能未释放的VideoCapture对象
        if cap_ref[0] is not None:
            try:
                cap_ref[0].release()
            except Exception:
                pass
        event.set()
        return 0

    return result[0]


def speed_opencv_frame(url, timeout=5):
    try:
        # cap = cv2.VideoCapture(url)
        # 设置OpenCV的缓冲区大小和预读取帧数
        cap = cv2.VideoCapture(url, cv2.CAP_FFMPEG)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
        cap.set(cv2.CAP_PROP_FRAME_COUNT, 1)  # 只读取一帧
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'H264'))  # 使用H264解码器

        # 设置超时和其他优化参数
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout * 1000)

        # 读取第一帧
        ret, frame = cap.read()
        if ret:
            height, width = frame.shape[:2]
            cap.release()
            return width, height, frame
        cap.release()
    except Exception as e:
        pass
    return 0, 0, None


def speed_av_frame(url, timeout=5):
    try:
        container = av.open(
            url,
            options={
                "probesize": "4096"
            },
            timeout=timeout
        )
        if container.streams.video:
            stream = container.streams.video[0]
            # 直接获取第一帧
            frame = next(container.decode(stream))
            img = frame.to_ndarray(format='rgb24')
            return stream.width, stream.height, img
    except Exception as e:
        pass
    return 0, 0, None


# --------------- 性能测试框架 ---------------
def run_perf_test(method, name, url):
    logger.info(f"Testing {name}...")
    times = []
    successes = 0
    round_times = []

    for _ in range(TEST_ROUNDS):
        start = time.perf_counter()
        if name == "speed_opencv_frame" or name == "speed_av_frame":
            w, h, _ = method(url)
        else:
            w = method(url)
        duration = (time.perf_counter() - start) * 1000

        if w != 0:
            round_times.append(duration)
            times.append(duration)
            successes += 1
        else:
            round_times.append(None)
    if successes > 0:
        avg = mean(times)
        filtered_times = [t for t in times if abs(
            t - mean(times)) <= 2 * np.std(times)]
        filtered_avg = mean(filtered_times) if filtered_times else avg

        logger.info(f"✅ Success rate: {successes}/{TEST_ROUNDS}")
        logger.info(
            f"过滤异常值后 - Avg: {filtered_avg:.2f}ms | Min: {min(filtered_times):.2f}ms | Max: {max(filtered_times):.2f}ms\n")
        return filtered_avg, round_times

    logger.info("❌ All attempts failed\n")
    return None, round_times


# --------------- 执行测试 ---------------
def main(url):
    methods = [
        (speed_av, "PyAv"),
        (speed_ffprobe, "FFprobe"),
        (speed_opencv, "OpenCV"),
        # (speed_opencv_frame, "speed_opencv_frame"),
        # (speed_av_frame, "speed_av_frame"),
    ]
    results = {}
    all_round_times = {}

    for func, name in methods:
        avg_time, round_times = run_perf_test(func, name, url)
        if avg_time is not None:
            results[name] = avg_time
            all_round_times[name] = round_times

    if results:
        logger.info("\n=== 性能比较 ===")
        slowest_time = max(results.values())
        for name, time_ms in sorted(results.items(), key=lambda x: x[1]):
            relative_speed = slowest_time / time_ms
            logger.info(
                f"{name:<25} | {time_ms:.2f}ms | {relative_speed:.2f}x faster")

        plot_performance_comparison(results, all_round_times)
    else:
        logger.info("没有成功的测试结果")


def plot_performance_comparison(results, all_round_times):
    plt.figure(figsize=(12, 6))
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'x', '*']

    for i, (name, times) in enumerate(all_round_times.items()):
        valid_times = [(j, t) for j, t in enumerate(times) if t is not None]
        if valid_times:
            x_values, y_values = zip(*valid_times)
            plt.plot(x_values, y_values, label=name,
                     marker=markers[i], color=colors[i], linewidth=2)

    plt.title('各方法测试时间对比', fontsize=16)
    plt.xlabel('测试轮次', fontsize=14)
    plt.ylabel('耗时 (毫秒)', fontsize=14)
    plt.grid(True, alpha=0.7)
    plt.legend()
    plt.xticks(range(TEST_ROUNDS))
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    urls = """http://[2409:8087:2001:20:2800:0:df6e:eb20]/ott.mobaibox.com/PLTV/4/224/3221228499/index.m3u8
    http://[2409:8087:2001:20:2800:0:df6e:eb20]:80/PLTV/1/224/3221230948/1.m3u8
    """
    url_list = [url.strip() for url in urls.splitlines() if url.strip()]
    for url in url_list:
        logger.info(f"\n开始测试 URL: {url}")
        main(url)
