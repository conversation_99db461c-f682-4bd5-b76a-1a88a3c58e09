2025-08-09 16:22:41 [INFO] 测试日志写入位置
2025-08-09 16:23:46 [INFO] 已从 /Users/<USER>/Desktop/IPTV/M3uStreamChecker/iptv_pyServer/config/subscriptions.json 加载 20 条订阅信息
2025-08-09 16:23:46 [INFO] 启动 IPTV 服务器...
2025-08-09 16:23:46 [INFO] 网页功能已启用
2025-08-09 16:23:46 [INFO] 处理图片: /Users/<USER>/Desktop/IPTV/M3uStreamChecker/iptv_pyServer/config/ad.jpeg, 输出至: /Users/<USER>/Desktop/IPTV/M3uStreamChecker/iptv_pyServer/iptv
2025-08-09 16:23:46 [INFO] 视频参数: 1920x1080, 5fps, 片段2秒, 总长10秒, 比特率500k
2025-08-09 16:23:46 [INFO] 步骤1: 图片转MP4...
2025-08-09 16:23:47 [INFO] 步骤2: MP4转HLS...
2025-08-09 16:23:47 [INFO] 成功生成1个TS片段文件
2025-08-09 16:23:47 [INFO] M3U8播放列表已创建完成: /Users/<USER>/Desktop/IPTV/M3uStreamChecker/iptv_pyServer/iptv/playlist.m3u8
2025-08-09 16:23:47 [INFO] 广告视频流已初始化，流地址: http://*************:8000/iptv/playlist.m3u8
2025-08-09 16:23:47 [INFO] 广告视频功能已启用
2025-08-09 16:23:47 [INFO] 后台调度线程已启动
2025-08-09 16:23:47 [INFO] 启动Waitress服务器于 0.0.0.0:8000
2025-08-09 16:23:47 [INFO] 工作线程: 4
2025-08-09 16:23:47 [INFO] 连接限制: 1000
2025-08-09 16:23:47 [INFO] 开始重新测试超时URL...
2025-08-09 16:23:47 [INFO] 发现 17724 个超时URL需要重新测试
