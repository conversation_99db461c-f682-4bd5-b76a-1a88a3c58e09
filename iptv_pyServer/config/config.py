import os
from pathlib import Path
from pydantic import BaseModel
from typing import ClassVar
import logging


class PathConfig(BaseModel):
    """文件路径配置类"""
    # config files
    config_dir: ClassVar[Path] = Path(
        os.path.dirname(os.path.abspath(__file__)))
    local_m3u_txt_dir: Path = config_dir / "local"
    local_ott_dir: Path = config_dir / "ott"
    bad_resolution_file: Path = config_dir / "bad_resolution.txt"
    channel_template_file: Path = config_dir / "channelTemplate.md"
    channel_modify_file: Path = config_dir / "channel_modify.txt"
    name_replace_file: Path = config_dir / "name_replace.txt"
    subscribe_file: Path = config_dir / "subscribe.txt"
    timeout_url_file: Path = config_dir / "timeout_url.pkl"
    dianbo_file: Path = config_dir / "dianbo.txt"
    subscriptions_file: Path = config_dir / "subscriptions.json"
    ad_image_file: Path = config_dir / "ad.jpeg"
    m3u8_filename: str = "playlist.m3u8"  # M3U8广告流文件名

    # iptv files
    iptv_dir: Path = config_dir.parent / "iptv"
    streams_file: Path = iptv_dir / "streams.csv"
    ott_csv: Path = iptv_dir / "ott.csv"
    not_in_channel_streams_file: Path = iptv_dir / "not_in_channel_streams.csv"
    dianbo_json: Path = iptv_dir / "dianbo.json"
    ipv4_ipv6_csv: Path = iptv_dir / "ipv4&ipv6.csv"
    ipv4_ipv6_m3u: Path = iptv_dir / "ipv4&ipv6.m3u"
    ipv4_csv: Path = iptv_dir / "ipv4.csv"
    ipv4_m3u: Path = iptv_dir / "ipv4.m3u"
    ipv6_csv: Path = iptv_dir / "ipv6.csv"
    ipv6_m3u: Path = iptv_dir / "ipv6.m3u"

    # logs files
    log_dir: Path = config_dir.parent / "logs"


class ParamConfig(BaseModel):
    """应用参数配置类"""
    thread_num: int = 20
    black_keywords: list[str] = ["ottrrs"]
    enable_url_connectivity_check: bool = True
    resolution_method: str = "ffprobe"  # 可选值: av/ffprobe/opencv
    log_level: int = logging.INFO  # 默认日志级别
    min_width_resolution: int = 1280  # 最小宽度分辨率要求
    default_timeout: int = 5  # 默认超时时间（秒）
    connectivity_timeout: int = 3  # 连通性检查超时时间（秒）


class LogConfig(BaseModel):
    """日志配置类"""
    log_file: str = 'iptv-server.log'  # 日志文件名
    max_bytes: int = 10 * 1024 * 1024  # 单个日志文件最大大小 (10MB)
    backup_count: int = 5  # 备份文件数量
    console: bool = True  # 是否输出到控制台
    file: bool = True  # 是否输出到文件


class ServerConfig(BaseModel):
    """服务器配置类"""
    host: str = "0.0.0.0"
    port: int = 8000
    threads: int = 4
    connection_limit: int = 1000
    cleanup_interval: int = 30
    channel_timeout: int = 120
    enable_web: bool = True
    enable_ad_video: bool = True


class Config(BaseModel):
    """总配置类"""
    path_config: PathConfig = PathConfig()
    param_config: ParamConfig = ParamConfig()
    log_config: LogConfig = LogConfig()
    server_config: ServerConfig = ServerConfig()


cfg = Config()
