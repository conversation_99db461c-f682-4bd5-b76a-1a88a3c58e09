import os
import json
import time
import hashlib
import random
import pyotp
import re
from flask import request, Response, jsonify, send_file
from config.config import cfg
from logger import logger
from utils import get_request_host_url
from urllib.parse import urlparse, urlunparse

# 存储已生成的授权信息
# {sha_id: {"expire_time": timestamp, "days": 30, "created_at": timestamp}}
SUBSCRIPTIONS = {}

# TOTP配置
# 随机生成的密钥，实际应用中应安全存储
TOTP_SECRET = "NIC5WB6XHXWIW26Z6KM7JED7KIGLP6PC"
# 创建TOTP对象
totp = pyotp.TOTP(TOTP_SECRET)


def generate_totp():
    """
    生成当前的TOTP验证码

    Returns:
        str: 6位数字验证码
    """
    return totp.now()


def verify_totp(code):
    """
    验证TOTP动态密码，允许前后1个时间窗口的误差

    Args:
        code (str): 用户提供的验证码

    Returns:
        bool: 验证是否通过
    """
    return totp.verify(code, valid_window=1)


def generate_short_hash():
    """
    生成8位短SHA字符串

    Returns:
        str: 8位SHA字符串
    """
    # 生成随机种子 + 时间戳，确保唯一性
    seed = str(random.randint(1, 1000000)) + str(time.time())
    # 生成SHA256 hash
    sha_hash = hashlib.sha256(seed.encode()).hexdigest()
    # 取8位
    return sha_hash[:8]


def get_ott_provinces():
    """
    获取config/ott文件夹中的省份文件列表

    Returns:
        list: 省份名称列表（不包含.m3u扩展名）
    """
    try:
        # 获取当前文件所在目录，然后构建config/ott路径
        current_dir = os.path.dirname(__file__)
        ott_dir = os.path.join(current_dir, 'config', 'ott')
        logger.debug(f"查找OTT目录: {ott_dir}")

        if not os.path.exists(ott_dir):
            logger.warning(f"OTT目录不存在: {ott_dir}")
            return []

        provinces = []
        for filename in os.listdir(ott_dir):
            if filename.endswith('.m3u'):
                # 去掉.m3u扩展名
                province_name = filename[:-4]
                provinces.append(province_name)

        # 按名称排序
        provinces.sort()
        logger.debug(f"找到 {len(provinces)} 个省份文件: {provinces}")
        return provinces
    except Exception as e:
        logger.error(f"获取省份列表失败: {str(e)}")
        return []


def generate_subscription(days, province=None):
    """
    生成订阅信息

    Args:
        days (int): 订阅天数
        province (str, optional): 选择的省份名称

    Returns:
        dict: 返回包含订阅信息的字典
    """
    # 确保天数有效
    days = int(days)
    if days > 1:
        days = days + 1
    # 生成8位SHA字符串作为订阅ID
    sha_id = generate_short_hash()
    while sha_id in SUBSCRIPTIONS:  # 确保不重复
        sha_id = generate_short_hash()

    # 计算过期时间
    current_time = int(time.time())
    expire_time = current_time + days * 24 * 60 * 60

    # 转换为人类可读格式
    created_date = time.strftime(
        "%Y-%m-%d %H:%M:%S", time.localtime(current_time))
    expire_date = time.strftime(
        "%Y-%m-%d %H:%M:%S", time.localtime(expire_time))

    # 存储订阅信息
    SUBSCRIPTIONS[sha_id] = {
        "expire_time": expire_time,  # 保留时间戳用于程序逻辑
        "expire_date": expire_date,  # 人类可读格式
        "days": days,
        "created_at": current_time,  # 保留时间戳用于程序逻辑
        "created_date": created_date  # 人类可读格式
    }

    # 获取请求的URL，优先使用当前请求的主机URL
    base_url = get_request_host_url()

    # 生成新的订阅链接格式：http://server_ip:8000/filename.m3u?token=sha_id
    subscription_urls = {
        "dianbo": f"{base_url}/{cfg.path_config.dianbo_json.name}?token={sha_id}",
        "all": f"{base_url}/{cfg.path_config.ipv4_ipv6_m3u.name}?token={sha_id}",
        "ipv4": f"{base_url}/{cfg.path_config.ipv4_m3u.name}?token={sha_id}",
        "ipv6": f"{base_url}/{cfg.path_config.ipv6_m3u.name}?token={sha_id}"
    }

    # 如果选择了省份，添加省份对应的订阅链接
    if province and province.strip():
        province_filename = f"{province.strip()}.m3u"
        subscription_urls[province] = f"{base_url}/ott/{province_filename}?token={sha_id}"

    # 保存订阅信息到本地（可选）
    try:
        save_subscriptions_to_file()
    except Exception as e:
        logger.warning(f"保存订阅信息失败: {str(e)}")

    # 返回生成的授权信息
    return {
        "success": True,
        "message": f"授权成功，订阅有效期至 {expire_date}",
        "sha_id": sha_id,
        "expire_time": expire_time,
        "expire_date": expire_date,
        "subscription_urls": subscription_urls
    }


def is_subscription_valid(sha_id):
    """
    快速检查订阅是否有效

    Args:
        sha_id (str): 订阅SHA ID

    Returns:
        bool: 订阅是否有效
    """
    if not sha_id or sha_id not in SUBSCRIPTIONS:
        return False

    subscription = SUBSCRIPTIONS[sha_id]
    current_time = int(time.time())

    # 使用时间戳进行比较
    return current_time <= subscription["expire_time"]


def save_subscriptions_to_file():
    """
    保存订阅信息到本地文件
    """
    # 构建要保存的数据
    data_to_save = {}
    for sha_id, subscription in SUBSCRIPTIONS.items():
        data_to_save[sha_id] = subscription

    # 确保目录存在
    os.makedirs(os.path.dirname(
        cfg.path_config.subscriptions_file), exist_ok=True)

    with open(cfg.path_config.subscriptions_file, 'w', encoding='utf-8') as f:
        json.dump(data_to_save, f, ensure_ascii=False,
                  indent=4, sort_keys=True)

    logger.info(f"订阅信息已保存到: {cfg.path_config.subscriptions_file}")

    # 每次保存后计算最近一条订阅的过期时间
    if SUBSCRIPTIONS:
        latest_sub = list(SUBSCRIPTIONS.values())[-1]
        expire_date = latest_sub.get('expire_date', 'Unknown')
        logger.debug(f"最近一条订阅过期时间: {expire_date}")


def load_subscriptions_from_file():
    """
    从本地文件加载订阅信息
    """
    global SUBSCRIPTIONS

    if os.path.exists(cfg.path_config.subscriptions_file):
        try:
            with open(cfg.path_config.subscriptions_file, 'r', encoding='utf-8') as f:
                SUBSCRIPTIONS = json.load(f)
            logger.info(
                f"已从 {cfg.path_config.subscriptions_file} 加载 {len(SUBSCRIPTIONS)} 条订阅信息")
        except Exception as e:
            logger.error(f"加载订阅信息失败: {str(e)}")
            SUBSCRIPTIONS = {}
    else:
        logger.info(f"订阅信息文件不存在: {cfg.path_config.subscriptions_file}")
        SUBSCRIPTIONS = {}


def cleanup_expired_subscriptions():
    """
    清理已过期的订阅信息

    Returns:
        tuple: (清理数量, 剩余数量)
    """
    global SUBSCRIPTIONS

    # 记录清理前数量
    before_count = len(SUBSCRIPTIONS)
    current_time = int(time.time())

    # 过滤出未过期的订阅
    valid_subscriptions = {}
    expired_subscriptions = []

    for sha_id, subscription in SUBSCRIPTIONS.items():
        if current_time <= subscription["expire_time"]:
            valid_subscriptions[sha_id] = subscription
        else:
            expired_subscriptions.append({
                "sha_id": sha_id,
                "expire_date": subscription.get("expire_date", "未知")
            })

    # 计算清理数量
    removed_count = before_count - len(valid_subscriptions)

    # 如果有清理，则更新并保存
    if removed_count > 0:
        # 记录被清理的订阅详情
        for expired in expired_subscriptions:
            logger.info(
                f"清理过期订阅: {expired['sha_id']}, 过期时间: {expired['expire_date']}")

        SUBSCRIPTIONS = valid_subscriptions
        try:
            save_subscriptions_to_file()
            logger.info(f"已清理 {removed_count} 条过期订阅，剩余 {len(SUBSCRIPTIONS)} 条")
        except Exception as e:
            logger.error(f"保存清理后的订阅信息失败: {str(e)}")

    return removed_count, len(SUBSCRIPTIONS)


def process_json_file(full_path, token):
    """
    读取并处理JSON文件，为lives数组中的URL添加token参数
    并将URL中的主机部分(IP:端口)替换为当前请求者使用的主机地址

    Args:
        full_path (str): JSON文件的完整路径
        token (str): 要添加的token值

    Returns:
        Response: 包含处理后JSON内容的HTTP响应
    """
    try:
        # 读取JSON文件内容
        with open(full_path, 'r', encoding='utf-8') as f:
            json_content = json.load(f)

        # 获取当前请求的主机URL
        base_url = get_request_host_url()

        # 处理JSON中的lives URL
        if isinstance(json_content, dict) and 'lives' in json_content:
            for live in json_content.get('lives', []):
                if 'url' in live and live['url']:
                    # 直接用base_url替换原URL的主机部分，并添加token
                    original_url = urlparse(live['url'])
                    # 使用base_url的主机部分+原URL的路径部分+token
                    base_parsed = urlparse(base_url)
                    live['url'] = urlunparse((
                        base_parsed.scheme,  # 使用base_url的协议
                        base_parsed.netloc,  # 使用base_url的主机和端口
                        original_url.path,   # 保留原URL的路径
                        original_url.params,  # 保留原URL的参数
                        f"token={token}",    # 添加token查询参数
                        original_url.fragment  # 保留原URL的片段
                    ))

        # 直接从内存返回修改后的JSON
        return Response(
            json.dumps(json_content, ensure_ascii=False),
            mimetype='application/json'
        )
    except Exception as e:
        logger.error(f"处理JSON文件失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


def process_m3u_file(full_path, token_valid):
    """
    处理M3U文件，根据token有效性决定返回正常文件还是替换URL后的文件

    Args:
        full_path (str): M3U文件的完整路径
        token_valid (bool): token是否有效

    Returns:
        Response或send_file结果: 文件内容的HTTP响应
    """
    try:
        # 如果token有效，直接返回文件
        if token_valid:
            return send_file(full_path, mimetype='application/x-mpegurl')

        # token无效时，读取文件内容并替换URL
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 获取广告流URL
        server_url = get_request_host_url()
        ad_stream_url = f"{server_url}/iptv/{cfg.path_config.m3u8_filename}"
        
        # 匹配非#开头的URL行（包含://的行）并替换为广告URL
        pattern = r'^(?!#).*?://.*$'
        modified_content = re.sub(pattern, ad_stream_url, content, flags=re.MULTILINE)

        # 获取原始文件名
        filename = os.path.basename(full_path)
        
        # 返回修改后的内容，并设置下载文件名
        response = Response(
            modified_content,
            mimetype='application/x-mpegurl'
        )
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    except Exception as e:
        logger.error(f"处理M3U文件失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


# 启动时加载订阅信息
load_subscriptions_from_file()
