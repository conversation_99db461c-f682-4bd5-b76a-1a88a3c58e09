#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import pandas as pd
import socket
from typing import Optional, List
from logger import logger
import random
import shutil
import numpy as np
from PIL import Image
import urllib.request
import urllib.parse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm
from pathlib import Path
import speed_test
import pickle
import datetime
from config.config import cfg
from check_streams import standardize_channel_name

# 全局变量
BAD_RESOLUTION_LIST = []
TIMEOUT_DICT = {}
MIN_WIDTH_RESOLUTION = 1280
COUNT = 0
epg_url = 'http://epg.51zmt.top:8000/e.xml'


def init_global_variables():
    """初始化全局变量"""
    global BAD_RESOLUTION_LIST, TIMEOUT_DICT
    # 初始化TIMEOUT_DICT
    try:
        with open(cfg.path_config.timeout_url_file, 'rb') as f:
            TIMEOUT_DICT = pickle.load(f)
    except (FileNotFoundError, EOFError):
        TIMEOUT_DICT = {}

    # 初始化BAD_RESOLUTION_LIST
    read_bad_resolution_list()


def add_to_TIMEOUT_DICT(url):
    if url not in TIMEOUT_DICT.keys():
        TIMEOUT_DICT[url] = 1
    else:
        TIMEOUT_DICT[url] = 2


def write_m3u(file_path: str, df):
    """将DataFrame写入m3u文件"""
    m3u_content = [f'#EXTM3U x-tvg-url="{epg_url}"']
    current_time_str = datetime.datetime.now().strftime("%m-%d %H:%M")
    m3u_content.append(
        f'#EXTINF:-1 group-title="更新时间",{current_time_str}\n{df.iloc[0]["URL"]}')
    # 按照m3u格式写入流信息
    for _, row in df.iterrows():
        channel_name = row['name']
        channel_url = row['URL']
        channel_group = row['group']  # 使用DataFrame中的group值
        tvg_logo = f'https://live.fanmingming.cn/tv/{channel_name}.png'
        m3u_entry = f'#EXTINF:-1 group-title="{channel_group}" tvg-name="{channel_name}" tvg-logo="{tvg_logo}",{channel_name}\n{channel_url}'
        m3u_content.append(m3u_entry)

    content = '\n'.join(m3u_content)
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)


def check_support_ipv6():
    original_family = socket.AF_UNSPEC
    result = False

    try:
        # 临时设置socket默认族为IPv6
        socket._socket.AF_UNSPEC = socket.AF_INET6

        # 尝试访问百度
        response = urllib.request.urlopen("http://www.baidu.com", timeout=5)
        logger.info(f"IPv6连接成功！状态码: {response.status}")
        result = True
    except Exception as e:
        logger.error(f"IPv6连接失败: {type(e).__name__} - {str(e)}")
        result = False
    finally:
        # 恢复原始设置
        socket._socket.AF_UNSPEC = original_family

    return result


def check_ip_type(url):
    ipv4_supported = False
    ipv6_supported = False
    try:
        host = urllib.parse.urlparse(url).hostname
        if not host:
            return False, False

        # 检查是否是IPv4地址
        try:
            socket.inet_pton(socket.AF_INET, host)
            return True, False  # 如果是IPv4地址，直接返回
        except socket.error:
            pass

        # 检查是否是IPv6地址
        try:
            socket.inet_pton(socket.AF_INET6, host)
            return False, True  # 如果是IPv6地址，直接返回
        except socket.error:
            pass

        # 如果不是IP地址而是域名
        addr_info = socket.getaddrinfo(
            host, None, socket.AF_UNSPEC, socket.SOCK_STREAM)
        for info in addr_info:
            if info[0] == socket.AF_INET:
                ipv4_supported = True
            elif info[0] == socket.AF_INET6:
                ipv6_supported = True
        return ipv4_supported, ipv6_supported
    except:
        return False, False


def save_stream_image(img, base_dir: Path) -> None:
    """保存视频流的第一帧图像"""
    # 创建images目录
    images_dir = base_dir / 'images'
    images_dir.mkdir(exist_ok=True)

    # 生成随机文件名
    while True:
        random_number = random.randint(1, 999999)
        image_path = images_dir / f"{random_number}.png"
        if not image_path.exists():
            break

    # 将numpy数组转换为PIL图像并保存
    if img is not None and isinstance(img, np.ndarray):
        image = Image.fromarray(img)
        image.save(image_path)


def sort_df(df):
    name_list = []
    template_path = Path(__file__).resolve().parent / \
        'config' / 'channelTemplate.md'
    with open(template_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line.startswith('- '):
                channel_name = standardize_channel_name(line[2:])
                name_list.append(channel_name)

    # 先按照name的顺序排序
    df['name'] = pd.Categorical(df['name'], categories=name_list, ordered=True)
    df_sorted = df.sort_values('name')

    result_df = pd.DataFrame()
    for name, group in df_sorted.groupby('name', observed=True):
        # 对每个组内的数据按resolution降序、response_time升序排序
        sorted_group = group.sort_values(
            ['resolution', 'ipv6', 'response_time'], ascending=[False, False, True])
        result_df = pd.concat([result_df, sorted_group])

    return result_df


def read_bad_resolution_list():
    """从bad_resolution.txt文件读取URL列表到BAD_RESOLUTION_LIST"""
    global BAD_RESOLUTION_LIST

    try:
        with open(cfg.path_config.bad_resolution_file, 'r', encoding='utf-8') as f:
            BAD_RESOLUTION_LIST = [line.strip().split('$')[0]
                                   for line in f if line.strip()]
    except FileNotFoundError:
        logger.warning(f"警告: {cfg.path_config.bad_resolution_file} 文件不存在")
        BAD_RESOLUTION_LIST = []


def write_bad_resolution_list():
    """将BAD_RESOLUTION_LIST中的URL列表写入bad_resolution.txt文件"""
    # 使用set去重URL列表
    unique_urls = list(set(BAD_RESOLUTION_LIST))

    with open(cfg.path_config.bad_resolution_file, 'w', encoding='utf-8') as f:
        for url in unique_urls:
            f.write(f"{url}\n")


def get_stream_info(row: pd.Series) -> Optional[pd.Series]:
    global COUNT
    response_time = None
    pdS = None
    url = str(row['URL'])

    try:
        if any(keyword in url for keyword in cfg.param_config.black_keywords):
            return None
        if url in BAD_RESOLUTION_LIST or (url in TIMEOUT_DICT.keys() and TIMEOUT_DICT[url] == 2):
            return None
        t1 = time.time()
        COUNT += 1
        timeout = 5
        if cfg.param_config.enable_url_connectivity_check:
            # timeout = 10
            is_seccess, new_url = speed_test.check_url_connectivity(url)
        else:
            is_seccess = True
            new_url = url
        if is_seccess:
            t2 = time.time()
            if cfg.param_config.resolution_method == "av":
                w = speed_test.speed_av(new_url, timeout)
            elif cfg.param_config.resolution_method == "ffprobe":
                w = speed_test.speed_ffprobe(new_url, timeout)
            else:
                w = speed_test.speed_opencv(new_url, timeout)
            response_time = int((time.time() - t2) * 1000)
            logger.debug(
                f'连通性耗时:{int((t2 - t1) * 1000)} ms,获取分辨率耗时:{response_time} ms')
            if w >= MIN_WIDTH_RESOLUTION:
                row['resolution'] = w
                row['response_time'] = response_time
                pdS = row
                if url in TIMEOUT_DICT.keys():
                    TIMEOUT_DICT.pop(url)
            elif w > 0:
                BAD_RESOLUTION_LIST.append(url)
            else:
                logger.debug(f"分辨率过低:w={w} {url}")
                add_to_TIMEOUT_DICT(url)
        else:
            add_to_TIMEOUT_DICT(url)
    except Exception as e:
        logger.error(f"处理流信息时出错: {url}, 错误: {str(e)}")
    return pdS


def iptv_checker():
    start_time = time.time()
    # 初始化全局变量
    init_global_variables()
    df = pd.read_csv(cfg.path_config.streams_file)
    if df.empty:
        logger.warning("未找到有效的流，请检查输入文件夹中的文件格式")
        return

    total = len(df)
    with ThreadPoolExecutor(max_workers=cfg.param_config.thread_num) as executor:
        futures = {executor.submit(
            get_stream_info, row): idx for idx, row in df.iterrows()}
        available_list = []
        with tqdm(total=total, desc="检测进度", unit="个", mininterval=0.2) as pbar:
            for future in as_completed(futures):
                result = future.result()
                if result is not None:
                    available_list.append(result)
                pbar.update(1)
    write_bad_resolution_list()
    # 保存TIMEOUT_DICT
    with open(cfg.path_config.timeout_url_file, "wb") as f:
        pickle.dump(TIMEOUT_DICT, f)

    if available_list:  # 只在有流的情况下创建文件
        available_df = pd.DataFrame(available_list)
        ipv4_list = []
        ipv6_list = []
        for _, row in available_df.iterrows():
            url = str(row['URL'])
            ipv4_supported, ipv6_supported = check_ip_type(url)
            ipv4_list.append(ipv4_supported)
            ipv6_list.append(ipv6_supported)
        available_df['ipv4'] = ipv4_list
        available_df['ipv6'] = ipv6_list
        available_df = sort_df(available_df)

        # 筛选并保存IPv4支持的流
        ipv4_df = available_df[available_df['ipv4'] == True].copy()
        if not ipv4_df.empty:
            ipv4_df.to_csv(cfg.path_config.ipv4_csv,
                           index=False, encoding='utf-8')
            write_m3u(cfg.path_config.ipv4_m3u, ipv4_df)
            logger.info(f"已将 {len(ipv4_df)} 个IPv4可用流写入到ipv4.m3u")
        # 筛选并保存IPv6支持的流
        ipv6_df = available_df[available_df['ipv6'] == True].copy()
        if not ipv6_df.empty:
            ipv6_df.to_csv(cfg.path_config.ipv6_csv,
                           index=False, encoding='utf-8')
            write_m3u(cfg.path_config.ipv6_m3u, ipv6_df)
            logger.info(f"已将 {len(ipv6_df)} 个IPv6可用流写入到ipv6.m3u")
        # 保存所有可用流
        available_df.to_csv(cfg.path_config.ipv4_ipv6_csv,
                            index=False, encoding='utf-8')
        write_m3u(cfg.path_config.ipv4_ipv6_m3u, available_df)
        logger.info(
            f"已将 {len(available_df)}/{COUNT}/{total} 个可用流写入到ipv4&ipv6.m3u")

    # 输出统计信息
    elapsed_time = time.time() - start_time
    logger.info(f'测试完成，总耗时: {elapsed_time:.2f} 秒')


def retest_timeout_url():
    """重新测试TIMEOUT_DICT中的URL连通性"""
    start_time = time.time()
    logger.info("开始重新测试超时URL...")

    # 初始化全局变量
    init_global_variables()

    # 检查TIMEOUT_DICT是否为空
    if not TIMEOUT_DICT:
        logger.info("没有需要重新测试的超时URL")
        return

    # 创建一个临时的URL列表用于测试
    timeout_urls = list(TIMEOUT_DICT.keys())
    total = len(timeout_urls)
    logger.info(f"发现 {total} 个超时URL需要重新测试")

    # 使用多线程测试URL
    recovered_urls = []
    with ThreadPoolExecutor(max_workers=cfg.param_config.thread_num) as executor:
        # 为每个ParseResult对象创建一个future
        futures = {}
        for parsed_url in timeout_urls:
            # 确保URL是字符串格式
            url_str = parsed_url.geturl() if hasattr(
                parsed_url, 'geturl') else str(parsed_url)
            futures[executor.submit(
                speed_test.check_url_connectivity, url_str, 3)] = parsed_url

        # 处理完成的future
        with tqdm(total=total, desc="重新测试进度", unit="个", mininterval=0.2) as pbar:
            for future in as_completed(futures):
                parsed_url = futures[future]
                try:
                    is_connected = future.result()  # 直接获取连接结果
                    if is_connected:  # 如果URL能够连通
                        if parsed_url in TIMEOUT_DICT:
                            TIMEOUT_DICT.pop(parsed_url)
                            recovered_urls.append(parsed_url)
                except Exception as e:
                    pass  # 忽略异常，保持URL在TIMEOUT_DICT中
                pbar.update(1)

    # 保存更新后的TIMEOUT_DICT

    with open(cfg.path_config.timeout_url_file, "wb") as f:
        pickle.dump(TIMEOUT_DICT, f)

    # 输出统计信息
    elapsed_time = time.time() - start_time
    logger.info(
        f"重新测试完成，恢复连通的URL: {len(recovered_urls)}/{total}，总耗时: {elapsed_time:.2f} 秒")


if __name__ == '__main__':
    # retest_timeout_url()
    iptv_checker()
