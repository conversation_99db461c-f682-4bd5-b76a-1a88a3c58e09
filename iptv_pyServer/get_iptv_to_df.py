#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import pandas as pd
from pathlib import Path
import re
from urllib.parse import urlparse
import json
import netifaces
from logger import logger
from config.config import cfg
from utils import get_server_url


def is_valid_url(url):
    """
    使用 urllib.parse.urlparse 检查 URL 是否符合标准的 URL 语法。
    """
    parsed_url = urlparse(url)
    # 检查 URL 是否至少包含协议、域名和路径
    return bool(parsed_url.scheme and parsed_url.netloc)


def use_proxy_if_needed(url):
    """检查URL是否需要使用代理，如果需要则返回代理配置"""
    # if 'raw.githubusercontent.com' in url:
    #     proxy = 'http://127.0.0.1:10808'
    #     return {'http': proxy, 'https': proxy}
    return None


def extract_urls(text):
    # 使用 # 和 ; 以及逗号作为分隔符分割文本
    parts = re.split(r'[#,;]+', text)

    # 正则表达式：只匹配 http, https, rtmp 开头的 URL，移除了rtsp
    pattern = re.compile(r'^(https?|rtmp)://[^\s\'"<>]+')

    # 用于过滤不需要的结尾，忽略 URL 中的查询参数部分（?后面的内容）
    undesired_ext_pattern = re.compile(r'\.(mp4|mkv)(\?.*)?$', re.IGNORECASE)

    urls = []
    for part in parts:
        part = part.strip()
        # 如果包含 $，只取 $ 之前的部分
        if '$' in part:
            part = part.split('$')[0].strip()
        # 使用正则匹配 URL，提取有效部分
        m = pattern.match(part)
        if m:
            url = m.group(0)
            # 过滤以 .mp4 或 .mkv 结尾（忽略查询参数）
            if undesired_ext_pattern.search(url):
                continue
            # 检查 URL 是否符合标准语法
            if is_valid_url(url):
                urls.append(url)
    return urls


def parse_content(content: str, subscribe_url) -> list:
    """解析直播源内容，返回流数据列表"""
    streams_data = []
    current_group = '未分类'
    current_name = '未知频道'
    # 处理m3u格式
    if content.startswith('#EXTM3U'):
        for line in content.splitlines():
            line = line.strip()
            if not line:
                continue
            if line.startswith('#EXTINF'):
                # 提取tvg-name属性作为名称
                if 'tvg-name="' in line:
                    match = re.search(r'tvg-name="(.*?)"', line)
                    if match:
                        current_name = match.group(1)
                elif 'tvg-id="' in line:
                    match = re.search(r'tvg-id="(.*?)"', line)
                    if match:
                        current_name = match.group(1)
                if current_name == '未知频道':
                    current_name = line.split(',')[-1].strip()

                # 提取group-title属性
                if 'group-title="' in line:
                    group_match = re.search(r'group-title="(.*?)"', line)
                    if group_match:
                        current_group = group_match.group(1)
                else:
                    current_group = '未分类'

            else:
                urls = extract_urls(line)
                for u in urls:
                    streams_data.append({
                        'group': current_group,
                        'name': current_name,
                        'URL': u,
                        'subs': subscribe_url
                    })
                    current_group = '未分类'
                    current_name = '未知频道'
    else:
        # 处理txt格式
        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            if '#genre#' in line and line.endswith('#genre#'):
                current_group = line.replace(',#genre#', '').strip()
                continue

            if ',' in line:
                parts = line.split(',', 1)
                channel_name = parts[0].strip()
                urls = extract_urls(parts[1].strip())
                for u in urls:
                    streams_data.append({
                        'group': current_group,
                        'name': channel_name,
                        'URL': u,
                        'subs': subscribe_url
                    })

    return streams_data


def get_subscribe(subscribe_file: Path):
    if not subscribe_file.exists():
        logger.warning(f"订阅文件不存在: {subscribe_file}")
        return

    urls = []
    with open(subscribe_file, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()
            if line and not line.startswith('#'):
                urls.append(line)

    all_streams = []
    for url in urls:
        logger.info(f"正在获取订阅源: {url}")
        try:
            response = requests.get(
                url, timeout=10, proxies=use_proxy_if_needed(url))
            response.raise_for_status()
            content = response.text
        except Exception as e:
            logger.error(f"获取URL {url} 内容失败: {str(e)}")
            continue

        if content:
            path_parts = urlparse(url).path.split('/')
            subs_url = path_parts[1] if len(
                path_parts) > 1 else urlparse(url).netloc
            streams = parse_content(content, subs_url)
            all_streams.extend(streams)

    return all_streams


def get_local_m3u_txt(local_dir: Path):
    if not local_dir.exists():
        logger.warning(f"本地文件夹不存在: {local_dir}")
        return []

    all_streams = []
    for file_path in local_dir.glob('*'):
        if file_path.is_file():
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                streams = parse_content(content, f'local/{file_path.name}')
                all_streams.extend(streams)
                logger.info(f"已读取文件: {file_path.name}")
            except Exception as e:
                logger.error(f"读取文件 {file_path.name} 失败: {str(e)}")

    return all_streams


def save_df(streams_list, file_path) -> None:
    if not streams_list:
        logger.warning(f"没有 {file_path} 数据可保存")
        return
        
    df = pd.DataFrame(streams_list)
    # 确保URL列的值是唯一的
    original_count = len(df)
    if not df.empty:
        df = df.drop_duplicates(subset=['URL'], keep='first')
    logger.info(f"获取到 {original_count} 个 {file_path} 原始流，去重后剩余 {len(df)} 个唯一流")

    # 保存DataFrame到CSV文件
    df.to_csv(file_path, index=False, encoding='utf-8')
    logger.info(f"已将 {file_path} 数据保存到: {file_path}")


def extract_lives_urls() -> None:
    """从远程JSON文件提取直播源URL并添加到subscribe.txt"""

    try:
        # 读取dianbo.txt中的URL列表
        with open(cfg.path_config.dianbo_file, 'r', encoding='utf-8') as f:
            json_urls = [line.strip() for line in f if line.strip()
                         and not line.startswith('#')]

        if not json_urls:
            logger.warning("dianbo.txt中没有找到有效的JSON URL")
            return

        # 获取所有JSON文件中的URL
        new_urls = []
        for json_url in json_urls:
            try:
                response = requests.get(
                    json_url, timeout=10, proxies=use_proxy_if_needed(json_url))
                response.raise_for_status()
                data = response.json()

                if 'lives' in data:
                    for live in data['lives']:
                        if 'url' in live:
                            new_urls.append(live['url'].strip())
            except Exception as e:
                logger.error(f"处理URL {json_url} 时出错: {str(e)}")
                continue

        if not new_urls:
            logger.warning("未在JSON文件中找到有效的直播源URL")
            return

        # 读取现有的subscribe.txt
        existing_urls = set()
        try:
            with open(cfg.path_config.subscribe_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        existing_urls.add(line)
        except FileNotFoundError:
            pass

        # 找出需要添加的新URL
        new_urls_set = set(new_urls)
        all_urls = sorted(new_urls_set.union(existing_urls))

        # 清空并重新写入所有URL到文件
        with open(cfg.path_config.subscribe_file, 'w', encoding='utf-8') as f:
            for url in all_urls:
                f.write(f'{url}\n')

        logger.info(f"成功添加 {len(new_urls_set)} 个新的直播源URL到subscribe.txt")

    except FileNotFoundError:
        logger.error(f"未找到文件: {cfg.path_config.dianbo_file}")
    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def get_ott_streams(ott_dir: Path):
    """读取 ott 文件夹中的所有直播源文件"""
    if not ott_dir.exists():
        logger.warning(f"ott 文件夹不存在: {ott_dir}")
        return []

    all_streams = []
    for file_path in ott_dir.glob('*'):
        if file_path.is_file() and file_path.suffix.lower() in ['.m3u', '.txt']:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                # 使用文件名（不包含扩展名和路径）作为 subs 值
                file_name = file_path.stem
                streams = parse_content(content, file_name)
                all_streams.extend(streams)
                logger.info(f"已读取 ott 文件: {file_path.name}，获取到 {len(streams)} 个直播源")
            except Exception as e:
                logger.error(f"读取 ott 文件 {file_path.name} 失败: {str(e)}")

    return all_streams


def get_iptv_to_df(url="http://*************:4141/jvzi.json"):
    download_json(url)
    extract_lives_urls()
    all_streams = []
    local_list = get_local_m3u_txt(cfg.path_config.local_m3u_txt_dir)
    if local_list:
        all_streams.extend(local_list)
    subscribe_list = get_subscribe(cfg.path_config.subscribe_file)
    if subscribe_list:
        all_streams.extend(subscribe_list)
    save_df(all_streams, cfg.path_config.streams_file)
    
    # 读取 ott 文件夹的数据，但单独保存
    ott_list = get_ott_streams(cfg.path_config.local_ott_dir)
    if ott_list:
        logger.info(f"从 ott 文件夹获取到 {len(ott_list)} 个直播源")
        save_df(ott_list, cfg.path_config.ott_csv)



def download_json(url):
    try:
        response = requests.get(
            url, timeout=10, proxies=use_proxy_if_needed(url))
        response.raise_for_status()
        data = response.json()

        server_url = get_server_url()
        # 修改 lives 数组中的 URL
        data['lives'] = [
            {
                "name": "ipv4&ipv6直播源",
                "type": 0,
                "url": f"{server_url}/{cfg.path_config.ipv4_ipv6_m3u.name}",
                "playerType": 1,
                # "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
                # "logo": "https://epg.v1.mk/logo/{name}.png"
            },
            {
                "name": "IPv4直播源",
                "type": 0,
                "url": f"{server_url}/{cfg.path_config.ipv4_m3u.name}",
                "playerType": 1
            },
            {
                "name": "IPv6直播源",
                "type": 0,
                "url": f"{server_url}/{cfg.path_config.ipv6_m3u.name}",
                "playerType": 1
            }
        ]

        with open(cfg.path_config.dianbo_json, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"JSON文件已保存到: {cfg.path_config.dianbo_json}")
    except Exception as e:
        logger.error(f"下载异常: {str(e)}")


if __name__ == '__main__':
    get_iptv_to_df()
