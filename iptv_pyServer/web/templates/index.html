<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV订阅授权</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1f6feb;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #1f6feb;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0d5bdb;
        }
        .btn.active {
            background-color: #0d5bdb;
            box-shadow: 0 0 0 3px rgba(31, 111, 235, 0.3);
        }
        .custom-days {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .custom-days input {
            flex: 1;
            max-width: 50%;
        }
        #one-day-btn, #custom-btn {
            min-width: 80px;
            flex: 0 0 auto;
            width: 25%;
        }
        .result-box {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            min-height: 150px;
            white-space: pre-wrap;
            word-break: break-all;
            cursor: pointer;
            position: relative;
        }
        .result-box:hover {
            background-color: #f0f0f0;
        }
        .result-box::after {
            content: "点击复制内容";
            position: absolute;
            right: 10px;
            bottom: 10px;
            font-size: 12px;
            color: #999;
            opacity: 0.7;
        }
        .copied-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            display: none;
            z-index: 100;
        }
        .subscription-link {
            margin-top: 10px;
            padding: 10px;
            background-color: #e9f5ff;
            border-radius: 4px;
            word-break: break-all;
        }
        @media (max-width: 480px) {
            .button-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>IPTV订阅授权</h1>
        
        <div class="form-group">
            <label for="auth-code">验证码</label>
            <input type="text" id="auth-code" placeholder="请输入6位验证码" maxlength="6">
        </div>
        
        <div class="form-group">
            <label>订阅时长</label>
            <div class="button-grid">
                <button class="btn subscription-btn" data-type="1month">1个月</button>
                <button class="btn subscription-btn" data-type="3months">3个月</button>
                <button class="btn subscription-btn" data-type="6months">6个月</button>
                <button class="btn subscription-btn" data-type="9months">9个月</button>
                <button class="btn subscription-btn" data-type="12months">12个月</button>
                <button class="btn subscription-btn" data-type="permanent">永久</button>
            </div>
        </div>
        
        <div class="form-group">
            <div class="custom-days">
                <button class="btn" id="one-day-btn">1天</button>
                <input type="number" id="custom-days" placeholder="自定义月份" min="1">
                <button class="btn" id="custom-btn">确认</button>
            </div>
        </div>
        
        <div class="result-box" id="result-box">
            <!-- 结果将在这里显示 -->
            欢迎使用IPTV订阅授权系统。请输入验证码并选择订阅时长，生成订阅链接。
        </div>
    </div>

    <div class="copied-message" id="copied-message">已复制到剪贴板</div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const authCodeInput = document.getElementById('auth-code');
            const customDaysInput = document.getElementById('custom-days');
            const customBtn = document.getElementById('custom-btn');
            const oneDayBtn = document.getElementById('one-day-btn');
            const resultBox = document.getElementById('result-box');
            const copiedMessage = document.getElementById('copied-message');
            const subscriptionBtns = document.querySelectorAll('.subscription-btn');
            
            // 为订阅按钮添加点击事件
            subscriptionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 获取天数
                    let days = 0;
                    const type = this.dataset.type;
                    
                    // 转换为天数
                    if (type === '1month') days = 30;
                    else if (type === '3months') days = 90;
                    else if (type === '6months') days = 180;
                    else if (type === '9months') days = 270;
                    else if (type === '12months') days = 365;
                    else if (type === 'permanent') days = 36500;
                    
                    // 移除其他按钮的active类
                    subscriptionBtns.forEach(b => b.classList.remove('active'));
                    
                    // 添加active类到当前按钮
                    this.classList.add('active');
                    
                    // 发送授权请求
                    authorize(days);
                });
            });
            
            // 为自定义天数按钮添加点击事件
            customBtn.addEventListener('click', function() {
                const months = customDaysInput.value;
                if (!months || isNaN(months) || months < 1) {
                    resultBox.textContent = '请输入有效的月份（至少1个月）';
                    return;
                }
                
                // 将月份转换为天数（每月30天）
                const days = parseInt(months) * 30;
                
                // 发送自定义天数授权请求
                authorize(days);
            });
            
            // 为1天按钮添加点击事件
            oneDayBtn.addEventListener('click', function() {
                // 发送1天授权请求
                authorize(1);
            });
            
            // 为结果框添加点击复制功能
            resultBox.addEventListener('click', function() {
                const text = resultBox.textContent;
                if (text && text !== '处理中，请稍候...' && 
                    !text.includes('欢迎使用IPTV订阅授权系统')) {
                    copyToClipboard(text);
                    
                    // 显示复制成功提示
                    copiedMessage.style.display = 'block';
                    setTimeout(() => {
                        copiedMessage.style.display = 'none';
                    }, 2000);
                }
            });
            
            // 复制文本到剪贴板
            function copyToClipboard(text) {
                // 创建临时textarea元素
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
            }
            
            // 授权函数
            function authorize(days) {
                const code = authCodeInput.value;
                if (!code || code.length !== 6) {
                    resultBox.textContent = '请输入6位验证码';
                    return;
                }
                
                // 显示加载状态
                resultBox.textContent = '处理中，请稍候...';
                
                // 打印发送的数据，用于调试
                console.log('发送请求数据:', { code, days });
                
                // 发送请求
                fetch('/web/api/authorize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        days: parseInt(days) // 确保发送的是整数
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 构建订阅链接显示内容
                        let resultText = `${data.message}\n\n订阅链接：\n`;
                        
                        // 添加不同类型的订阅链接
                        resultText += `点播: ${data.subscription_urls.dianbo}\n`;
                        resultText += `所有频道: ${data.subscription_urls.all}\n`;
                        resultText += `仅IPv4: ${data.subscription_urls.ipv4}\n`;
                        resultText += `仅IPv6: ${data.subscription_urls.ipv6}`;
                        
                        resultBox.textContent = resultText;
                    } else {
                        resultBox.textContent = data.message;
                    }
                })
                .catch(error => {
                    resultBox.textContent = '请求失败，请稍后再试';
                    console.error('Error:', error);
                });
            }
        });
    </script>
</body>
</html> 