import os
import time
from flask import Blueprint, render_template, jsonify, request
from logger import logger
from subscription import generate_subscription, verify_totp, generate_totp

# 创建蓝图
web_bp = Blueprint('web', __name__, 
                  template_folder='templates',
                  static_folder='static',
                  url_prefix='/web')

@web_bp.route('/')
def index():
    """网页主页"""
    return render_template('index.html')

@web_bp.route('/api/authorize', methods=['POST'])
def authorize():
    """处理授权请求，生成订阅链接"""
    data = request.json
    code = data.get('code', '')  # 动态密码
    days = data.get('days', 0)   # 获取天数
    
    # 验证动态密码 (TOTP)
    if not verify_totp(code):
        logger.debug(f"动态密码验证失败: 输入={code}")
        
        return jsonify({
            "success": False,
            "message": "动态密码错误，请重新输入"
        })
    
    # 使用subscription模块生成订阅
    result = generate_subscription(days)
    
    # 将结果直接返回给前端
    return jsonify(result)