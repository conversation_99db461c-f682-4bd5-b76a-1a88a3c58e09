from collections import Counter
import pandas as pd
import os
import re
from logger import logger
from config.config import cfg
import opencc

t2s_converter = opencc.OpenCC('t2s')  # 繁体到简体

name_dict = {}


def read_name_replace():
    """读取name_replace.txt文件，返回频道名称映射字典"""
    global name_dict

    try:
        with open(cfg.path_config.name_replace_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                parts = line.split(',', 1)
                if len(parts) == 2:
                    name_dict[parts[0]] = parts[1].replace(
                        ' ', '').strip().upper()
    except FileNotFoundError:
        logger.warning(f"警告: {cfg.path_config.name_replace_file} 文件不存在")
    except Exception as e:
        logger.error(f"读取 {cfg.path_config.name_replace_file} 时出错: {str(e)}")


def standardize_channel_name(name):
    """标准化频道名称，去除空格和连字符，并将繁体中文转换为简体中文
    同时处理频道名称中的数字格式和后缀，如CCTV07、CCTV7高清等都标准化为CCTV7
    """
    if not isinstance(name, str):
        return ''

    # 基础清理：去除空格、连字符等，并将小写字母转换为大写字母
    name = re.sub(r'[ \-—]', '', name).strip().upper()

    # 如果name在字典中存在，使用字典中的值
    if name in name_dict:
        name = name_dict[name]

    # 繁体转简体
    name = t2s_converter.convert(name)

    # 处理CCTV频道格式：将CCTV01、CCTV1、CCTV01高清、CCTV1高清等统一为CCTV1
    cctv_pattern = re.compile(
        r'(CCTV)(0*)?(\d+)(K)?(\+)?(高清|超清|蓝光|HD|标清)?', re.IGNORECASE)
    if cctv_pattern.search(name):
        match = cctv_pattern.search(name)
        channel_prefix = match.group(1)  # CCTV
        channel_number = match.group(3)  # 数字部分
        channel_k = match.group(4) or ''  # 可能的K后缀
        channel_plus = match.group(5) or ''  # 可能的+号
        name = f"{channel_prefix}{channel_number}{channel_k}{channel_plus}"
        return name

    # 处理卫视频道
    weishi_pattern = re.compile(
        r'(.+卫视)(高清|超清|蓝光|HD|标清|4K|8K)?$', re.IGNORECASE)
    if weishi_pattern.search(name):
        name = weishi_pattern.sub(r'\1', name)
        return name

    # 处理其他频道格式，如省台、地方台等
    # 检查是否包含任何后缀关键词
    suffix_keywords = ['高清', '超清', '蓝光', 'HD', '标清',
                       '4K', '8K', '频道', '频高', '频标', '斯特', '咪咕', '台', '_ITV']
    if any(suffix in name for suffix in suffix_keywords):
        # 构建动态正则表达式，匹配任何后缀
        suffix_pattern = re.compile(
            r'(.+?)(' + '|'.join(suffix_keywords) + r')?$', re.IGNORECASE)
        if suffix_pattern.search(name):
            name = suffix_pattern.sub(r'\1', name)

    return name


def read_and_reverse_channel_dict(file_path):
    """读取频道模板文件并直接返回反向映射的字典"""
    reversed_channel_dict = {}
    current_category = None

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue

            if line.startswith('##'):
                current_category = line.replace('##', '').strip()
            elif line.startswith('-'):
                item = standardize_channel_name(line)
                if current_category and item:
                    if item in reversed_channel_dict:
                        logger.warning(f'警告：频道名称 {item} 重复')
                    reversed_channel_dict[item] = current_category

    return reversed_channel_dict


def read_channel_modify():
    """读取channel_modify.txt文件，返回URL到频道名的映射字典"""
    url_to_name = {}

    try:
        with open(cfg.path_config.channel_modify_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.endswith('#genre#'):
                    continue

                # 分割频道名和URL
                parts = line.split(',', 1)
                if len(parts) == 2:
                    url_to_name[parts[1].split('$')[0]] = parts[0]

        return url_to_name
    except FileNotFoundError:
        logger.warning(f"警告: {cfg.path_config.channel_modify_file} 文件不存在")
        return {}
    except Exception as e:
        logger.error(f"读取 {cfg.path_config.channel_modify_file} 时出错: {str(e)}")
        return {}


def process_streams(reversed_channel_dict, channel_modify_dict, csv_path):
    df = pd.read_csv(csv_path)
    # 标准化频道名称
    df['name'] = df.apply(lambda row: channel_modify_dict.get(
        row['URL'], row['name']), axis=1)
    read_name_replace()
    df['name'] = df['name'].apply(standardize_channel_name)
    mask = df['name'].isin(reversed_channel_dict.keys())

    # 保存不在模板中的频道到单独的CSV文件
    not_in_channel_df = df[~mask].copy()
    not_in_channel_df.to_csv(
        cfg.path_config.not_in_channel_streams_file, index=False, encoding='utf-8')

    # 更新group字段 - 直接使用reversed_channel_dict中的值
    filtered_df = df[mask].copy()
    filtered_df['group'] = filtered_df['name'].apply(
        lambda x: reversed_channel_dict[x])
    filtered_df.to_csv(csv_path, index=False, encoding='utf-8')

    unmatched_counts = not_in_channel_df['name'].value_counts()
    logger.info("\n=== 未匹配频道的频次统计 ===")
    for name, count in unmatched_counts[unmatched_counts > 5].items():
        logger.info(f"{name}: {count}次")

    logger.info(
        f"\n=== 处理结果 ===\n原始记录数: {len(df)} 条; 过滤后保留: {len(filtered_df)} 条; 删除记录数: {len(not_in_channel_df)} 条")
    logger.info(f"删除的频道已保存到: {cfg.path_config.not_in_channel_streams_file}")

    # 找出模板中存在但实际数据中未使用的频道（即reversed_channel_dict中的keys与filtered_df['name']的差集）
    template_channels = set(reversed_channel_dict.keys())
    used_channels = set(filtered_df['name'])
    unused_channels = list(template_channels - used_channels)
    logger.info(
        f"\n=== 未使用的频道 ===\n共有 {len(unused_channels)} 个频道在模板中存在但未在数据中使用")
    return unused_channels


def update_output_file(unused_channels, template_path):
    if not unused_channels:
        return

    with open(template_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    # 创建新的内容，排除未使用的频道
    new_lines = []
    for line in lines:
        original_line = line
        stripped_line = line.strip()

        # 保留空行和其他非频道行
        if not stripped_line or not stripped_line.startswith('- '):
            new_lines.append(original_line)
            continue

        # 处理频道行
        channel_name = stripped_line.replace('- ', '').strip()
        if channel_name not in unused_channels:
            new_lines.append(original_line)

    # 写入更新后的内容
    with open(template_path, 'w', encoding='utf-8') as file:
        file.writelines(new_lines)

    logger.info(f'channelTemplate.md中删除 {len(unused_channels)} 个未使用的频道')


def check_streams():
    reversed_channel_dict = read_and_reverse_channel_dict(
        cfg.path_config.channel_template_file)
    channel_modify_dict = read_channel_modify()
    unused_channels = process_streams(
        reversed_channel_dict, channel_modify_dict, cfg.path_config.streams_file)
    unused_channels2 = process_streams(
        reversed_channel_dict, channel_modify_dict, cfg.path_config.ott_csv)
    # update_output_file(unused_channels, cfg.path_config.channel_template_file)


if __name__ == '__main__':
    check_streams()
