#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import request
from logger import logger
import netifaces


def get_server_url(port: int = 8000):
    """获取服务器URL，格式为http://ip地址:8000"""
    ip_addr = '***********'
    try:
        # 获取默认网关接口
        default_interface = netifaces.gateways(
        )['default'][netifaces.AF_INET][1]
        # 获取该接口的地址信息
        addrs = netifaces.ifaddresses(default_interface)
        # 获取IPv4地址
        tmp_ip_addr = addrs[netifaces.AF_INET][0]['addr']
        if tmp_ip_addr.startswith('192.168'):
            ip_addr = tmp_ip_addr
    except Exception as e:
        logger.error(f"获取服务器URL失败: {e}")
    return f"http://{ip_addr}:{port}"


def get_request_host_url(port: int = 8000):
    """
    获取当前请求的主机URL (协议://主机:端口)

    当在请求上下文中时，返回请求的主机URL
    当不在请求上下文中时，返回get_server_url()

    主要用于subscription_urls，确保外网用户获取到的订阅地址是外网的地址

    Returns:
        str: 请求的主机URL或服务器URL
    """
    try:
        if request:
            host = request.headers.get('Host', request.host)
            scheme = request.headers.get('X-Forwarded-Proto', request.scheme)
            return f"{scheme}://{host}"
    except Exception as e:
        logger.error(f"获取请求URL失败: {str(e)}")
    return get_server_url(port)
